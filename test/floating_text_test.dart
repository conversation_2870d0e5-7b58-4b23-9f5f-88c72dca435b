import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:digital_wooden_fish/models/floating_text.dart';
import 'package:digital_wooden_fish/widgets/floating_text_widget.dart';

void main() {
  group('FloatingText 优化测试', () {
    testWidgets('测试快速创建多个浮动文字实例', (WidgetTester tester) async {
      // 创建一个浮动文字模板
      final textTemplate = FloatingText(
        id: 'test_text',
        text: '测试文字',
        color: Colors.green,
        unlockThreshold: 0,
        rarity: FloatingTextRarity.basic,
        isUnlocked: true,
      );

      // 创建多个实例
      final instance1 = textTemplate.createInstance();
      final instance2 = textTemplate.createInstance();
      final instance3 = textTemplate.createInstance();

      // 验证每个实例都有唯一的instanceId
      expect(instance1.instanceId, isNot(equals(instance2.instanceId)));
      expect(instance2.instanceId, isNot(equals(instance3.instanceId)));
      expect(instance1.instanceId, isNot(equals(instance3.instanceId)));

      // 验证模板ID相同
      expect(instance1.id, equals(textTemplate.id));
      expect(instance2.id, equals(textTemplate.id));
      expect(instance3.id, equals(textTemplate.id));

      // 验证其他属性相同
      expect(instance1.text, equals(textTemplate.text));
      expect(instance1.color, equals(textTemplate.color));
      expect(instance1.rarity, equals(textTemplate.rarity));
    });

    testWidgets('测试FloatingTextManager最大数量限制', (WidgetTester tester) async {
      // 创建测试应用
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: FloatingTextManager(
              key: floatingTextManagerKey,
              child: Container(),
            ),
          ),
        ),
      );

      // 创建测试文字模板
      final textTemplate = FloatingText(
        id: 'test_text',
        text: '测试',
        color: Colors.blue,
        unlockThreshold: 0,
        rarity: FloatingTextRarity.basic,
        isUnlocked: true,
      );

      // 添加超过最大数量的文字
      const maxTexts = 8; // 对应代码中的 maxFloatingTexts
      const testPosition = Offset(100, 100);

      for (int i = 0; i < maxTexts + 3; i++) {
        showFloatingText(textTemplate, testPosition);
        await tester.pump();
      }

      // 验证文字数量不超过最大限制
      final currentCount = getCurrentFloatingTextCount();
      expect(currentCount, lessThanOrEqualTo(maxTexts));
    });

    testWidgets('测试浮动文字创建', (WidgetTester tester) async {
      // 创建测试应用
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: FloatingTextManager(
              key: floatingTextManagerKey,
              child: Container(),
            ),
          ),
        ),
      );

      // 等待widget构建完成
      await tester.pumpAndSettle();

      // 创建测试文字模板
      final textTemplate = FloatingText(
        id: 'test_text',
        text: '测试',
        color: Colors.red,
        unlockThreshold: 0,
        rarity: FloatingTextRarity.basic,
        isUnlocked: true,
      );

      const basePosition = Offset(200, 200);

      // 添加一个文字
      showFloatingText(textTemplate, basePosition);
      await tester.pump();

      // 验证有FloatingTextWidget被创建
      expect(find.byType(FloatingTextWidget), findsOneWidget);
    });

    test('测试FloatingText序列化', () {
      final originalText = FloatingText(
        id: 'test_id',
        text: '测试文字',
        color: Colors.orange,
        unlockThreshold: 100,
        rarity: FloatingTextRarity.gold,
        isUnlocked: true,
      );

      // 测试toJson
      final json = originalText.toJson();
      expect(json['id'], equals('test_id'));
      expect(json['instanceId'], isNotNull);
      expect(json['text'], equals('测试文字'));
      expect(json['unlockThreshold'], equals(100));
      expect(json['rarity'], equals(FloatingTextRarity.gold.index));
      expect(json['isUnlocked'], isTrue);

      // 测试fromJson
      final restoredText = FloatingText.fromJson(json);
      expect(restoredText.id, equals(originalText.id));
      expect(restoredText.instanceId, equals(originalText.instanceId));
      expect(restoredText.text, equals(originalText.text));
      expect(
        restoredText.color.toARGB32(),
        equals(originalText.color.toARGB32()),
      );
      expect(
        restoredText.unlockThreshold,
        equals(originalText.unlockThreshold),
      );
      expect(restoredText.rarity, equals(originalText.rarity));
      expect(restoredText.isUnlocked, equals(originalText.isUnlocked));
    });
  });
}
