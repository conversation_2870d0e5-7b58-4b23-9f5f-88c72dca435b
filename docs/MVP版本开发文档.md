# 电子木鱼 MVP版本开发文档

## 🎯 项目概述
**项目名称**：电子木鱼 (Digital Wooden Fish)  
**技术栈**：Flutter  
**目标平台**：Android + iOS  
**开发目标**：创建一个极简的木鱼点击APP，用户可以通过点击屏幕来模拟敲木鱼，获得治愈感  

---

## 📱 功能需求

### 核心功能
1. **全屏点击检测** - 用户点击屏幕任意位置都能触发木鱼敲击
2. **点击计数** - 记录今日点击次数和总点击次数
3. **木鱼收集系统** - 根据总点击次数解锁不同款式的木鱼
4. **数据持久化** - 本地保存用户数据，应用重启后数据不丢失
5. **每日重置** - 每天0点自动重置今日点击次数

### 界面要求
1. **主界面** - 显示木鱼图片、点击计数、设置按钮
2. **木鱼列表界面** - 展示所有木鱼款式和解锁状态
3. **设置界面** - 基本设置选项

---

## 🎨 UI设计规范

### 主界面布局
```
┌─────────────────────────────────┐
│  木鱼名称    今日:XX  总计:XXX  ⚙️│  ← 顶部状态栏
├─────────────────────────────────┤
│                                 │
│                                 │
│         🪵 木鱼图片              │  ← 中央点击区域
│                                 │
│                                 │
├─────────────────────────────────┤
│           [木鱼列表]             │  ← 底部按钮
└─────────────────────────────────┘
```

### 配色方案
- **主色调**：温暖棕色 (#8B4513)
- **背景色**：渐变色 (#F5E6D3 到 #E8D5B7)
- **文字色**：深棕色 (#5D2E0A)
- **按钮色**：浅棕色 (#D2B48C)

### 字体规范
- **标题**：18sp, 粗体
- **计数器**：16sp, 常规
- **按钮文字**：14sp, 常规

---

## 📊 数据模型

### WoodenFish 模型
```dart
class WoodenFish {
  final String id;
  final String name;
  final String imagePath;
  final int unlockThreshold;
  final FishRarity rarity;
  bool isUnlocked;
  
  WoodenFish({
    required this.id,
    required this.name,
    required this.imagePath,
    required this.unlockThreshold,
    required this.rarity,
    this.isUnlocked = false,
  });
}

enum FishRarity {
  basic,    // 基础
  bronze,   // 青铜
  silver,   // 白银
  gold,     // 黄金
  platinum, // 铂金
  diamond,  // 钻石
  master,   // 大师
  king,     // 王者
  rainbow,  // 彩虹
}
```

### UserData 模型
```dart
class UserData {
  int todayTaps;
  int totalTaps;
  String currentFishId;
  DateTime lastResetDate;
  Map<String, bool> unlockedFish;
  
  UserData({
    this.todayTaps = 0,
    this.totalTaps = 0,
    this.currentFishId = 'basic',
    DateTime? lastResetDate,
    Map<String, bool>? unlockedFish,
  }) : lastResetDate = lastResetDate ?? DateTime.now(),
       unlockedFish = unlockedFish ?? {'basic': true};
}
```

---

## 🗂️ 项目结构

```
lib/
├── main.dart                 # 应用入口
├── models/
│   ├── wooden_fish.dart      # 木鱼数据模型
│   └── user_data.dart        # 用户数据模型
├── screens/
│   ├── home_screen.dart      # 主界面
│   ├── fish_list_screen.dart # 木鱼列表界面
│   └── settings_screen.dart  # 设置界面
├── services/
│   └── storage_service.dart  # 本地存储服务
├── providers/
│   └── game_provider.dart    # 游戏状态管理
└── utils/
    └── constants.dart        # 常量定义
```

---

## 🎮 核心功能实现

### 1. 主界面功能
- **全屏点击检测**：使用 GestureDetector 包裹整个屏幕
- **点击动画**：木鱼图片缩放动画 (1.0 → 0.95 → 1.0)
- **计数更新**：每次点击增加今日和总计数
- **实时显示**：使用 Consumer 监听状态变化

### 2. 木鱼解锁机制
- **解锁条件**：基于总点击次数
- **解锁检查**：每次点击后检查是否有新木鱼解锁
- **解锁提示**：显示解锁成功的提示信息

### 3. 数据持久化
- **存储方式**：使用 SharedPreferences
- **存储内容**：用户数据、木鱼解锁状态
- **自动保存**：每次数据变化后自动保存

### 4. 每日重置
- **检查时机**：应用启动时
- **重置条件**：当前日期与上次重置日期不同
- **重置内容**：今日点击次数归零

---

## 📋 木鱼配置数据

### 木鱼列表配置
```dart
final List<WoodenFish> defaultFishList = [
  WoodenFish(
    id: 'basic',
    name: '基础木鱼',
    imagePath: 'assets/images/fish_basic.png',
    unlockThreshold: 0,
    rarity: FishRarity.basic,
    isUnlocked: true,
  ),
  WoodenFish(
    id: 'bronze',
    name: '青铜木鱼',
    imagePath: 'assets/images/fish_bronze.png',
    unlockThreshold: 100,
    rarity: FishRarity.bronze,
  ),
  WoodenFish(
    id: 'silver',
    name: '白银木鱼',
    imagePath: 'assets/images/fish_silver.png',
    unlockThreshold: 500,
    rarity: FishRarity.silver,
  ),
  WoodenFish(
    id: 'gold',
    name: '黄金木鱼',
    imagePath: 'assets/images/fish_gold.png',
    unlockThreshold: 1000,
    rarity: FishRarity.gold,
  ),
  WoodenFish(
    id: 'platinum',
    name: '铂金木鱼',
    imagePath: 'assets/images/fish_platinum.png',
    unlockThreshold: 2000,
    rarity: FishRarity.platinum,
  ),
  WoodenFish(
    id: 'diamond',
    name: '钻石木鱼',
    imagePath: 'assets/images/fish_diamond.png',
    unlockThreshold: 5000,
    rarity: FishRarity.diamond,
  ),
  WoodenFish(
    id: 'master',
    name: '大师木鱼',
    imagePath: 'assets/images/fish_master.png',
    unlockThreshold: 10000,
    rarity: FishRarity.master,
  ),
  WoodenFish(
    id: 'king',
    name: '王者木鱼',
    imagePath: 'assets/images/fish_king.png',
    unlockThreshold: 20000,
    rarity: FishRarity.king,
  ),
  WoodenFish(
    id: 'rainbow',
    name: '彩虹木鱼',
    imagePath: 'assets/images/fish_rainbow.png',
    unlockThreshold: 50000,
    rarity: FishRarity.rainbow,
  ),
];
```

---

## 🔧 技术实现要点

### 依赖包配置 (pubspec.yaml)
```yaml
dependencies:
  flutter:
    sdk: flutter
  provider: ^6.1.1
  shared_preferences: ^2.2.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true
  assets:
    - assets/images/
```

### 状态管理
- **使用 Provider** 进行状态管理
- **GameProvider** 管理游戏状态
- **Consumer** 监听状态变化并更新UI

### 动画效果
- **点击动画**：使用 AnimationController 和 Tween
- **动画时长**：200毫秒
- **动画曲线**：Curves.easeInOut

### 错误处理
- **图片加载失败**：显示默认占位图
- **数据加载失败**：使用默认数据
- **存储失败**：静默处理，不影响用户体验

---

## 📱 界面实现细节

### 主界面 (HomeScreen)
1. **顶部状态栏**
   - 左侧：当前木鱼名称
   - 中间：今日点击次数
   - 右侧：总点击次数和设置按钮

2. **中央点击区域**
   - 木鱼图片居中显示
   - 图片大小：250x250 像素
   - 点击时缩放动画

3. **底部按钮**
   - 木鱼列表按钮
   - 按钮样式：圆角矩形，浅棕色背景

### 木鱼列表界面 (FishListScreen)
1. **网格布局**
   - 每行2个木鱼卡片
   - 卡片间距：16像素

2. **木鱼卡片**
   - 木鱼图片
   - 木鱼名称
   - 解锁状态指示器
   - 未解锁显示进度条

3. **交互功能**
   - 点击已解锁木鱼切换使用
   - 点击未解锁木鱼显示解锁条件

### 设置界面 (SettingsScreen)
1. **设置项列表**
   - 版本信息
   - 关于应用
   - 联系我们

2. **列表样式**
   - 简洁的列表项
   - 分割线
   - 点击反馈

---

## 🎯 开发优先级

### 第一优先级 (核心功能)
1. 主界面点击功能
2. 计数器功能
3. 数据持久化
4. 基础木鱼显示

### 第二优先级 (扩展功能)
1. 木鱼解锁系统
2. 木鱼列表界面
3. 木鱼切换功能
4. 点击动画

### 第三优先级 (完善功能)
1. 设置界面
2. 每日重置功能
3. 错误处理
4. UI优化

---

## ✅ 验收标准

### 功能验收
- [ ] 点击屏幕能正常增加计数
- [ ] 计数数据能持久保存
- [ ] 木鱼能正常解锁和切换
- [ ] 每日重置功能正常工作
- [ ] 所有界面能正常导航

### 性能验收
- [ ] 点击响应时间 < 100ms
- [ ] 界面切换流畅无卡顿
- [ ] 内存使用合理
- [ ] 应用启动时间 < 3秒

### UI验收
- [ ] 界面布局符合设计规范
- [ ] 配色方案一致
- [ ] 字体大小合适
- [ ] 动画效果自然

---

## 🚀 部署要求

### Android配置
- **最低SDK版本**：21 (Android 5.0)
- **目标SDK版本**：34 (Android 14)
- **应用ID**：com.ai2six.woodenfish

### iOS配置
- **最低版本**：iOS 12.0
- **Bundle ID**：com.ai2six.woodenfish

### 资源要求
- **应用图标**：1024x1024 像素
- **木鱼图片**：512x512 像素，PNG格式
- **总包体大小**：< 50MB
