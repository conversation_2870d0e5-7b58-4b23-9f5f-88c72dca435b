import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';

/// 浮现文字稀有度枚举
enum FloatingTextRarity {
  basic, // 基础
  bronze, // 青铜
  silver, // 白银
  gold, // 黄金
  platinum, // 铂金
  diamond, // 钻石
  master, // 大师
  king, // 王者
  rainbow, // 彩虹
}

/// 浮现文字稀有度扩展方法
extension FloatingTextRarityExtension on FloatingTextRarity {
  /// 获取稀有度名称
  String get displayName {
    switch (this) {
      case FloatingTextRarity.basic:
        return '基础';
      case FloatingTextRarity.bronze:
        return '青铜';
      case FloatingTextRarity.silver:
        return '白银';
      case FloatingTextRarity.gold:
        return '黄金';
      case FloatingTextRarity.platinum:
        return '铂金';
      case FloatingTextRarity.diamond:
        return '钻石';
      case FloatingTextRarity.master:
        return '大师';
      case FloatingTextRarity.king:
        return '王者';
      case FloatingTextRarity.rainbow:
        return '彩虹';
    }
  }

  /// 获取稀有度颜色
  Color get color {
    switch (this) {
      case FloatingTextRarity.basic:
        return Colors.grey;
      case FloatingTextRarity.bronze:
        return const Color(0xFFCD7F32);
      case FloatingTextRarity.silver:
        return const Color(0xFFC0C0C0);
      case FloatingTextRarity.gold:
        return const Color(0xFFFFD700);
      case FloatingTextRarity.platinum:
        return const Color(0xFFE5E4E2);
      case FloatingTextRarity.diamond:
        return const Color(0xFFB9F2FF);
      case FloatingTextRarity.master:
        return const Color(0xFF654321);
      case FloatingTextRarity.king:
        return const Color(0xFF800080);
      case FloatingTextRarity.rainbow:
        return const Color(0xFFFF6B6B);
    }
  }
}

/// 浮现文字数据模型
class FloatingText {
  final String id; // 模板ID，用于识别文字类型
  final String instanceId; // 实例ID，每次创建实例时生成唯一ID
  final String text;
  final Color color;
  final int unlockThreshold;
  final FloatingTextRarity rarity;
  bool isUnlocked;

  FloatingText({
    required this.id,
    String? instanceId,
    required this.text,
    required this.color,
    required this.unlockThreshold,
    required this.rarity,
    this.isUnlocked = false,
  }) : instanceId = instanceId ?? const Uuid().v4();

  /// 从JSON创建FloatingText对象
  factory FloatingText.fromJson(Map<String, dynamic> json) {
    return FloatingText(
      id: json['id'] as String,
      instanceId: json['instanceId'] as String?,
      text: json['text'] as String,
      color: Color(json['color'] as int),
      unlockThreshold: json['unlockThreshold'] as int,
      rarity: FloatingTextRarity.values[json['rarity'] as int],
      isUnlocked: json['isUnlocked'] as bool? ?? false,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'instanceId': instanceId,
      'text': text,
      'color': color.toARGB32(),
      'unlockThreshold': unlockThreshold,
      'rarity': rarity.index,
      'isUnlocked': isUnlocked,
    };
  }

  /// 复制对象并修改某些属性
  FloatingText copyWith({
    String? id,
    String? instanceId,
    String? text,
    Color? color,
    int? unlockThreshold,
    FloatingTextRarity? rarity,
    bool? isUnlocked,
  }) {
    return FloatingText(
      id: id ?? this.id,
      instanceId: instanceId ?? this.instanceId,
      text: text ?? this.text,
      color: color ?? this.color,
      unlockThreshold: unlockThreshold ?? this.unlockThreshold,
      rarity: rarity ?? this.rarity,
      isUnlocked: isUnlocked ?? this.isUnlocked,
    );
  }

  /// 创建新实例（生成新的instanceId）
  FloatingText createInstance() {
    return FloatingText(
      id: id,
      instanceId: const Uuid().v4(),
      text: text,
      color: color,
      unlockThreshold: unlockThreshold,
      rarity: rarity,
      isUnlocked: isUnlocked,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FloatingText && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'FloatingText(id: $id, instanceId: $instanceId, text: $text, color: $color, unlockThreshold: $unlockThreshold, rarity: $rarity, isUnlocked: $isUnlocked)';
  }
}
