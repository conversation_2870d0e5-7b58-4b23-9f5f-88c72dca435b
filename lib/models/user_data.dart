/// 用户数据模型
class UserData {
  int todayTaps;
  int totalTaps;
  String currentFishId;
  DateTime lastResetDate;
  Map<String, bool> unlockedFish;
  Map<String, bool> unlockedTexts;

  UserData({
    this.todayTaps = 0,
    this.totalTaps = 0,
    this.currentFishId = 'basic',
    DateTime? lastResetDate,
    Map<String, bool>? unlockedFish,
    Map<String, bool>? unlockedTexts,
  }) : lastResetDate = lastResetDate ?? DateTime.now(),
       unlockedFish = unlockedFish ?? {'basic': true},
       unlockedTexts = unlockedTexts ?? {'merit_1': true, 'luck_1': true};

  /// 从JSON创建UserData对象
  factory UserData.fromJson(Map<String, dynamic> json) {
    return UserData(
      todayTaps: json['todayTaps'] as int? ?? 0,
      totalTaps: json['totalTaps'] as int? ?? 0,
      currentFishId: json['currentFishId'] as String? ?? 'basic',
      lastResetDate: json['lastResetDate'] != null
          ? DateTime.parse(json['lastResetDate'] as String)
          : DateTime.now(),
      unlockedFish: json['unlockedFish'] != null
          ? Map<String, bool>.from(json['unlockedFish'] as Map)
          : {'basic': true},
      unlockedTexts: json['unlockedTexts'] != null
          ? Map<String, bool>.from(json['unlockedTexts'] as Map)
          : {'merit_1': true, 'luck_1': true},
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'todayTaps': todayTaps,
      'totalTaps': totalTaps,
      'currentFishId': currentFishId,
      'lastResetDate': lastResetDate.toIso8601String(),
      'unlockedFish': unlockedFish,
      'unlockedTexts': unlockedTexts,
    };
  }

  /// 复制对象并修改某些属性
  UserData copyWith({
    int? todayTaps,
    int? totalTaps,
    String? currentFishId,
    DateTime? lastResetDate,
    Map<String, bool>? unlockedFish,
    Map<String, bool>? unlockedTexts,
  }) {
    return UserData(
      todayTaps: todayTaps ?? this.todayTaps,
      totalTaps: totalTaps ?? this.totalTaps,
      currentFishId: currentFishId ?? this.currentFishId,
      lastResetDate: lastResetDate ?? this.lastResetDate,
      unlockedFish: unlockedFish ?? Map<String, bool>.from(this.unlockedFish),
      unlockedTexts:
          unlockedTexts ?? Map<String, bool>.from(this.unlockedTexts),
    );
  }

  /// 增加点击次数
  void addTap() {
    todayTaps++;
    totalTaps++;
  }

  /// 解锁木鱼
  void unlockFish(String fishId) {
    unlockedFish[fishId] = true;
  }

  /// 检查木鱼是否已解锁
  bool isFishUnlocked(String fishId) {
    return unlockedFish[fishId] ?? false;
  }

  /// 切换当前使用的木鱼
  void switchFish(String fishId) {
    if (isFishUnlocked(fishId)) {
      currentFishId = fishId;
    }
  }

  /// 解锁浮现文字
  void unlockText(String textId) {
    unlockedTexts[textId] = true;
  }

  /// 检查浮现文字是否已解锁
  bool isTextUnlocked(String textId) {
    return unlockedTexts[textId] ?? false;
  }

  /// 重置今日点击次数
  void resetDailyTaps() {
    todayTaps = 0;
    lastResetDate = DateTime.now();
  }

  /// 检查是否需要重置今日点击次数
  bool shouldResetDaily() {
    final now = DateTime.now();
    final lastReset = lastResetDate;

    // 如果不是同一天，则需要重置
    return now.year != lastReset.year ||
        now.month != lastReset.month ||
        now.day != lastReset.day;
  }

  @override
  String toString() {
    return 'UserData(todayTaps: $todayTaps, totalTaps: $totalTaps, currentFishId: $currentFishId, lastResetDate: $lastResetDate, unlockedFish: $unlockedFish, unlockedTexts: $unlockedTexts)';
  }
}
