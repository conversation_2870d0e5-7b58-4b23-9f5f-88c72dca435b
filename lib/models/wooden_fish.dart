/// 木鱼稀有度枚举
enum FishRarity {
  basic, // 基础
  bronze, // 青铜
  silver, // 白银
  gold, // 黄金
  platinum, // 铂金
  diamond, // 钻石
  master, // 大师
  king, // 王者
}

/// 木鱼稀有度扩展方法
extension FishRarityExtension on FishRarity {
  /// 获取稀有度名称
  String get displayName {
    switch (this) {
      case FishRarity.basic:
        return '基础';
      case FishRarity.bronze:
        return '青铜';
      case FishRarity.silver:
        return '白银';
      case FishRarity.gold:
        return '黄金';
      case FishRarity.platinum:
        return '铂金';
      case FishRarity.diamond:
        return '钻石';
      case FishRarity.master:
        return '大师';
      case FishRarity.king:
        return '王者';
    }
  }

  /// 获取稀有度颜色
  int get color {
    switch (this) {
      case FishRarity.basic:
        return 0xFF8B4513; // 棕色
      case FishRarity.bronze:
        return 0xFFCD7F32; // 青铜色
      case FishRarity.silver:
        return 0xFFC0C0C0; // 银色
      case FishRarity.gold:
        return 0xFFFFD700; // 金色
      case FishRarity.platinum:
        return 0xFFE5E4E2; // 铂金色
      case FishRarity.diamond:
        return 0xFFB9F2FF; // 钻石蓝
      case FishRarity.master:
        return 0xFF9932CC; // 紫色
      case FishRarity.king:
        return 0xFFFF4500; // 橙红色
    }
  }
}

/// 木鱼数据模型
class WoodenFish {
  final String id;
  final String name;
  final String imagePath;
  final int unlockThreshold;
  final FishRarity rarity;
  bool isUnlocked;

  WoodenFish({
    required this.id,
    required this.name,
    required this.imagePath,
    required this.unlockThreshold,
    required this.rarity,
    this.isUnlocked = false,
  });

  /// 从JSON创建WoodenFish对象
  factory WoodenFish.fromJson(Map<String, dynamic> json) {
    return WoodenFish(
      id: json['id'] as String,
      name: json['name'] as String,
      imagePath: json['imagePath'] as String,
      unlockThreshold: json['unlockThreshold'] as int,
      rarity: FishRarity.values[json['rarity'] as int],
      isUnlocked: json['isUnlocked'] as bool? ?? false,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'imagePath': imagePath,
      'unlockThreshold': unlockThreshold,
      'rarity': rarity.index,
      'isUnlocked': isUnlocked,
    };
  }

  /// 复制对象并修改某些属性
  WoodenFish copyWith({
    String? id,
    String? name,
    String? imagePath,
    int? unlockThreshold,
    FishRarity? rarity,
    bool? isUnlocked,
  }) {
    return WoodenFish(
      id: id ?? this.id,
      name: name ?? this.name,
      imagePath: imagePath ?? this.imagePath,
      unlockThreshold: unlockThreshold ?? this.unlockThreshold,
      rarity: rarity ?? this.rarity,
      isUnlocked: isUnlocked ?? this.isUnlocked,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WoodenFish && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'WoodenFish(id: $id, name: $name, rarity: ${rarity.displayName}, unlockThreshold: $unlockThreshold, isUnlocked: $isUnlocked)';
  }
}
