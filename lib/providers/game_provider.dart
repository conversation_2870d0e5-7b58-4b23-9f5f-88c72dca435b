import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../models/user_data.dart';
import '../models/wooden_fish.dart';
import '../models/floating_text.dart';
import '../services/storage_service.dart';
import '../utils/constants.dart';

/// 游戏状态管理Provider
class GameProvider extends ChangeNotifier {
  UserData _userData = UserData();
  List<WoodenFish> _fishList = [];
  List<FloatingText> _textList = [];
  bool _isLoading = true;
  String? _errorMessage;

  // 浮现文字相关
  FloatingText? _currentFloatingText;

  // 动画控制器相关
  AnimationController? _tapAnimationController;
  Animation<double>? _scaleAnimation;

  /// 获取用户数据
  UserData get userData => _userData;

  /// 获取木鱼列表
  List<WoodenFish> get fishList => _fishList;

  /// 获取当前使用的木鱼
  WoodenFish? get currentFish {
    return _fishList.isNotEmpty
        ? _fishList.firstWhere(
            (fish) => fish.id == _userData.currentFishId,
            orElse: () => _fishList.first,
          )
        : null;
  }

  /// 获取今日点击次数
  int get todayTaps => _userData.todayTaps;

  /// 获取总点击次数
  int get totalTaps => _userData.totalTaps;

  /// 获取当前木鱼名称
  String get currentFishName => currentFish?.name ?? '基础木鱼';

  /// 是否正在加载
  bool get isLoading => _isLoading;

  /// 错误信息
  String? get errorMessage => _errorMessage;

  /// 获取缩放动画
  Animation<double>? get scaleAnimation => _scaleAnimation;

  /// 获取浮现文字列表
  List<FloatingText> get textList => _textList;

  /// 获取当前浮现文字
  FloatingText? get currentFloatingText => _currentFloatingText;

  /// 初始化Provider
  Future<void> init() async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // 初始化存储服务
      await StorageService.instance.init();

      // 加载用户数据
      _userData = await StorageService.instance.loadUserData();

      // 检查是否需要每日重置
      if (_userData.shouldResetDaily()) {
        _userData.resetDailyTaps();
        await _saveUserData();
      }

      // 初始化木鱼列表
      _initializeFishList();

      // 初始化浮现文字列表
      _initializeTextList();

      // 检查并解锁新木鱼
      await _checkAndUnlockFish();

      // 检查并解锁新浮现文字
      await _checkAndUnlockTexts();

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _errorMessage = '初始化失败: $e';
      _isLoading = false;
      notifyListeners();
    }
  }

  /// 初始化木鱼列表
  void _initializeFishList() {
    _fishList = FishConfig.defaultFishList.map((fish) {
      return fish.copyWith(isUnlocked: _userData.isFishUnlocked(fish.id));
    }).toList();
  }

  /// 初始化浮现文字列表
  void _initializeTextList() {
    _textList = FloatingTextConfig.defaultTextList.map((text) {
      return text.copyWith(isUnlocked: _userData.isTextUnlocked(text.id));
    }).toList();
  }

  /// 设置动画控制器
  void setAnimationController(AnimationController controller) {
    _tapAnimationController = controller;
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(parent: controller, curve: Curves.easeInOut));
  }

  /// 处理点击事件
  Future<void> onTap() async {
    try {
      // 播放点击动画
      if (_tapAnimationController != null) {
        await _tapAnimationController!.forward();
        await _tapAnimationController!.reverse();
      }

      // 增加点击次数
      _userData.addTap();

      // 生成随机浮现文字
      _generateRandomFloatingText();

      // 检查并解锁新木鱼
      await _checkAndUnlockFish();

      // 检查并解锁新浮现文字
      await _checkAndUnlockTexts();

      // 保存数据
      await _saveUserData();

      notifyListeners();
    } catch (e) {
      _errorMessage = '点击处理失败: $e';
      notifyListeners();
    }
  }

  /// 检查并解锁新木鱼
  Future<void> _checkAndUnlockFish() async {
    final unlockableFish = FishConfig.getUnlockableFish(
      _userData.totalTaps,
      _userData.unlockedFish,
    );

    for (String fishId in unlockableFish) {
      _userData.unlockFish(fishId);

      // 更新木鱼列表中的解锁状态
      final fishIndex = _fishList.indexWhere((fish) => fish.id == fishId);
      if (fishIndex != -1) {
        _fishList[fishIndex] = _fishList[fishIndex].copyWith(isUnlocked: true);
      }

      // 可以在这里添加解锁提示逻辑
      if (kDebugMode) {
        print('解锁新木鱼: ${FishConfig.getFishById(fishId)?.name}');
      }
    }
  }

  /// 检查并解锁新浮现文字
  Future<void> _checkAndUnlockTexts() async {
    final unlockableTexts = FloatingTextConfig.getUnlockableTexts(
      _userData.totalTaps,
      _userData.unlockedTexts,
    );

    for (String textId in unlockableTexts) {
      _userData.unlockText(textId);

      // 更新浮现文字列表中的解锁状态
      final textIndex = _textList.indexWhere((text) => text.id == textId);
      if (textIndex != -1) {
        _textList[textIndex] = _textList[textIndex].copyWith(isUnlocked: true);
      }

      // 可以在这里添加解锁提示逻辑
      if (kDebugMode) {
        print('解锁新浮现文字: ${FloatingTextConfig.getTextById(textId)?.text}');
      }
    }
  }

  /// 生成随机浮现文字
  void _generateRandomFloatingText() {
    final unlockedTexts = _textList.where((text) => text.isUnlocked).toList();
    if (unlockedTexts.isNotEmpty) {
      final random = Random();
      _currentFloatingText =
          unlockedTexts[random.nextInt(unlockedTexts.length)];
    }
  }

  /// 切换木鱼
  Future<void> switchFish(String fishId) async {
    try {
      if (_userData.isFishUnlocked(fishId)) {
        _userData.switchFish(fishId);
        await _saveUserData();
        notifyListeners();
      }
    } catch (e) {
      _errorMessage = '切换木鱼失败: $e';
      notifyListeners();
    }
  }

  /// 获取已解锁的木鱼列表
  List<WoodenFish> getUnlockedFishList() {
    return _fishList.where((fish) => fish.isUnlocked).toList();
  }

  /// 获取木鱼解锁进度
  double getFishUnlockProgress(String fishId) {
    final fish = FishConfig.getFishById(fishId);
    if (fish == null) return 0.0;

    if (_userData.isFishUnlocked(fishId)) return 1.0;

    if (fish.unlockThreshold == 0) return 1.0;

    return (_userData.totalTaps / fish.unlockThreshold).clamp(0.0, 1.0);
  }

  /// 获取已解锁的浮现文字列表
  List<FloatingText> getUnlockedTextList() {
    return _textList.where((text) => text.isUnlocked).toList();
  }

  /// 获取浮现文字解锁进度
  double getTextUnlockProgress(String textId) {
    final text = FloatingTextConfig.getTextById(textId);
    if (text == null) return 0.0;

    if (_userData.isTextUnlocked(textId)) return 1.0;

    if (text.unlockThreshold == 0) return 1.0;

    return (_userData.totalTaps / text.unlockThreshold).clamp(0.0, 1.0);
  }

  /// 保存用户数据
  Future<void> _saveUserData() async {
    try {
      await StorageService.instance.saveUserData(_userData);
    } catch (e) {
      if (kDebugMode) {
        print('保存用户数据失败: $e');
      }
    }
  }

  /// 重置游戏数据
  Future<void> resetGameData() async {
    try {
      await StorageService.instance.deleteUserData();
      _userData = UserData();
      _initializeFishList();
      _initializeTextList();
      await _saveUserData();
      notifyListeners();
    } catch (e) {
      _errorMessage = '重置数据失败: $e';
      notifyListeners();
    }
  }

  /// 清除错误信息
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  @override
  void dispose() {
    // 不要在这里dispose AnimationController，因为它是由外部创建和管理的
    super.dispose();
  }
}
