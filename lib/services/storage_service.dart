import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_data.dart';

/// 本地存储服务
class StorageService {
  static const String _userDataKey = 'user_data';
  static const String _firstLaunchKey = 'first_launch';
  
  static StorageService? _instance;
  SharedPreferences? _prefs;

  StorageService._();

  /// 获取单例实例
  static StorageService get instance {
    _instance ??= StorageService._();
    return _instance!;
  }

  /// 初始化存储服务
  Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  /// 确保已初始化
  void _ensureInitialized() {
    if (_prefs == null) {
      throw Exception('StorageService not initialized. Call init() first.');
    }
  }

  /// 保存用户数据
  Future<bool> saveUserData(UserData userData) async {
    try {
      _ensureInitialized();
      final jsonString = jsonEncode(userData.toJson());
      return await _prefs!.setString(_userDataKey, jsonString);
    } catch (e) {
      print('Error saving user data: $e');
      return false;
    }
  }

  /// 加载用户数据
  Future<UserData> loadUserData() async {
    try {
      _ensureInitialized();
      final jsonString = _prefs!.getString(_userDataKey);
      
      if (jsonString == null) {
        // 如果没有保存的数据，返回默认数据
        return UserData();
      }
      
      final jsonMap = jsonDecode(jsonString) as Map<String, dynamic>;
      return UserData.fromJson(jsonMap);
    } catch (e) {
      print('Error loading user data: $e');
      // 如果加载失败，返回默认数据
      return UserData();
    }
  }

  /// 检查是否是首次启动
  Future<bool> isFirstLaunch() async {
    try {
      _ensureInitialized();
      return !(_prefs!.getBool(_firstLaunchKey) ?? false);
    } catch (e) {
      print('Error checking first launch: $e');
      return true;
    }
  }

  /// 标记已完成首次启动
  Future<bool> markFirstLaunchComplete() async {
    try {
      _ensureInitialized();
      return await _prefs!.setBool(_firstLaunchKey, true);
    } catch (e) {
      print('Error marking first launch complete: $e');
      return false;
    }
  }

  /// 清除所有数据
  Future<bool> clearAllData() async {
    try {
      _ensureInitialized();
      return await _prefs!.clear();
    } catch (e) {
      print('Error clearing all data: $e');
      return false;
    }
  }

  /// 删除用户数据
  Future<bool> deleteUserData() async {
    try {
      _ensureInitialized();
      return await _prefs!.remove(_userDataKey);
    } catch (e) {
      print('Error deleting user data: $e');
      return false;
    }
  }

  /// 获取存储的字符串值
  Future<String?> getString(String key) async {
    try {
      _ensureInitialized();
      return _prefs!.getString(key);
    } catch (e) {
      print('Error getting string for key $key: $e');
      return null;
    }
  }

  /// 保存字符串值
  Future<bool> setString(String key, String value) async {
    try {
      _ensureInitialized();
      return await _prefs!.setString(key, value);
    } catch (e) {
      print('Error setting string for key $key: $e');
      return false;
    }
  }

  /// 获取存储的整数值
  Future<int?> getInt(String key) async {
    try {
      _ensureInitialized();
      return _prefs!.getInt(key);
    } catch (e) {
      print('Error getting int for key $key: $e');
      return null;
    }
  }

  /// 保存整数值
  Future<bool> setInt(String key, int value) async {
    try {
      _ensureInitialized();
      return await _prefs!.setInt(key, value);
    } catch (e) {
      print('Error setting int for key $key: $e');
      return false;
    }
  }

  /// 获取存储的布尔值
  Future<bool?> getBool(String key) async {
    try {
      _ensureInitialized();
      return _prefs!.getBool(key);
    } catch (e) {
      print('Error getting bool for key $key: $e');
      return null;
    }
  }

  /// 保存布尔值
  Future<bool> setBool(String key, bool value) async {
    try {
      _ensureInitialized();
      return await _prefs!.setBool(key, value);
    } catch (e) {
      print('Error setting bool for key $key: $e');
      return false;
    }
  }
}
