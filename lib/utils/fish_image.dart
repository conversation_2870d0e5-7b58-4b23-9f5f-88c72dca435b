import 'package:flutter/material.dart';

/// 木鱼图片显示Widget
class FishImage extends StatelessWidget {
  final String fishId;
  final double size;
  final bool isUnlocked;

  const FishImage({
    super.key,
    required this.fishId,
    this.size = 250.0,
    this.isUnlocked = true,
  });

  @override
  Widget build(BuildContext context) {
    final imagePath = 'assets/images/fish_$fishId.png';

    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(size * 0.1),
        boxShadow: size > 100
            ? [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ]
            : [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // 尝试加载真实图片，失败则显示占位符
          ClipRRect(
            borderRadius: BorderRadius.circular(size * 0.1),
            child: Image.asset(
              imagePath,
              width: size,
              height: size,
              fit: BoxFit.contain,
              errorBuilder: (context, error, stackTrace) {
                // 图片加载失败时显示占位符
                return _buildPlaceholder();
              },
            ),
          ),
          // 未解锁遮罩
          if (!isUnlocked)
            Container(
              width: size,
              height: size,
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.6),
                borderRadius: BorderRadius.circular(size * 0.1),
              ),
              child: Icon(Icons.lock, size: size * 0.3, color: Colors.white),
            ),
        ],
      ),
    );
  }

  /// 构建占位符
  Widget _buildPlaceholder() {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: _getFishColor(fishId),
        borderRadius: BorderRadius.circular(size * 0.1),
        border: Border.all(color: _getBorderColor(fishId), width: 3),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // 木鱼主体
          Container(
            width: size * 0.8,
            height: size * 0.6,
            decoration: BoxDecoration(
              color: _getFishColor(fishId).withValues(alpha: 0.8),
              borderRadius: BorderRadius.circular(size * 0.3),
              border: Border.all(color: _getBorderColor(fishId), width: 2),
            ),
          ),
          // 木鱼纹理
          Positioned(
            top: size * 0.25,
            child: Container(
              width: size * 0.6,
              height: size * 0.1,
              decoration: BoxDecoration(
                color: _getBorderColor(fishId).withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(size * 0.05),
              ),
            ),
          ),
          Positioned(
            top: size * 0.4,
            child: Container(
              width: size * 0.5,
              height: size * 0.08,
              decoration: BoxDecoration(
                color: _getBorderColor(fishId).withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(size * 0.04),
              ),
            ),
          ),
          Positioned(
            top: size * 0.55,
            child: Container(
              width: size * 0.4,
              height: size * 0.06,
              decoration: BoxDecoration(
                color: _getBorderColor(fishId).withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(size * 0.03),
              ),
            ),
          ),
          // 木鱼文字
          Positioned(
            bottom: size * 0.15,
            child: Text(
              _getFishEmoji(fishId),
              style: TextStyle(fontSize: size * 0.15),
            ),
          ),
        ],
      ),
    );
  }

  /// 根据木鱼ID获取颜色
  Color _getFishColor(String fishId) {
    switch (fishId) {
      case 'basic':
        return const Color(0xFF8B4513); // 棕色
      case 'bronze':
        return const Color(0xFFCD7F32); // 青铜色
      case 'silver':
        return const Color(0xFFC0C0C0); // 银色
      case 'gold':
        return const Color(0xFFFFD700); // 金色
      case 'platinum':
        return const Color(0xFFE5E4E2); // 铂金色
      case 'diamond':
        return const Color(0xFFB9F2FF); // 钻石色
      case 'master':
        return const Color(0xFF654321); // 深棕色
      case 'king':
        return const Color(0xFF800080); // 紫色
      default:
        return const Color(0xFF8B4513); // 默认棕色
    }
  }

  /// 根据木鱼ID获取边框颜色
  Color _getBorderColor(String fishId) {
    return _getFishColor(fishId).withValues(alpha: 0.7);
  }

  /// 根据木鱼ID获取表情符号
  String _getFishEmoji(String fishId) {
    switch (fishId) {
      case 'basic':
        return '🪵';
      case 'bronze':
        return '🥉';
      case 'silver':
        return '🥈';
      case 'gold':
        return '🥇';
      case 'platinum':
        return '💎';
      case 'diamond':
        return '💍';
      case 'master':
        return '👑';
      case 'king':
        return '🏆';
      default:
        return '🪵';
    }
  }
}

/// 木鱼占位符Widget（保持向后兼容）
class FishPlaceholder extends StatelessWidget {
  final String fishId;
  final double size;
  final bool isUnlocked;

  const FishPlaceholder({
    super.key,
    required this.fishId,
    this.size = 250.0,
    this.isUnlocked = true,
  });

  @override
  Widget build(BuildContext context) {
    // 直接使用新的 FishImage 组件
    return FishImage(fishId: fishId, size: size, isUnlocked: isUnlocked);
  }
}
