import 'package:flutter/material.dart';
import '../models/wooden_fish.dart';
import '../models/floating_text.dart';

/// 应用常量
class AppConstants {
  // 应用信息
  static const String appName = '数字木鱼';
  static const String appVersion = '1.0.0';
  static const String appDescription = '一个治愈系的点击APP';

  // 动画时长
  static const Duration tapAnimationDuration = Duration(milliseconds: 200);
  static const Duration pageTransitionDuration = Duration(milliseconds: 300);

  // 木鱼图片尺寸
  static const double fishImageSize = 250.0;
  static const double fishListImageSize = 80.0;

  // 间距
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;

  // 圆角
  static const double defaultBorderRadius = 12.0;
  static const double smallBorderRadius = 8.0;
  static const double largeBorderRadius = 16.0;

  // 字体大小
  static const double titleFontSize = 18.0;
  static const double counterFontSize = 16.0;
  static const double buttonFontSize = 14.0;
  static const double smallFontSize = 12.0;
}

/// 应用颜色主题
class AppColors {
  // 主色调
  static const Color primary = Color(0xFF8B4513); // 温暖棕色
  static const Color primaryLight = Color(0xFFA0522D);
  static const Color primaryDark = Color(0xFF5D2E0A);

  // 背景色
  static const Color backgroundStart = Color(0xFFF5E6D3);
  static const Color backgroundEnd = Color(0xFFE8D5B7);

  // 文字色
  static const Color textPrimary = Color(0xFF5D2E0A); // 深棕色
  static const Color textSecondary = Color(0xFF8B4513);
  static const Color textLight = Color(0xFFFFFFFF);

  // 按钮色
  static const Color button = Color(0xFFD2B48C); // 浅棕色
  static const Color buttonPressed = Color(0xFFC19A6B);

  // 状态色
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);

  // 卡片色
  static const Color cardBackground = Color(0xFFFFFBF5);
  static const Color cardShadow = Color(0x1A000000);

  // 分割线
  static const Color divider = Color(0xFFE0E0E0);

  // 创建渐变背景
  static const LinearGradient backgroundGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [backgroundStart, backgroundEnd],
  );
}

/// 默认木鱼列表配置
class FishConfig {
  static final List<WoodenFish> defaultFishList = [
    WoodenFish(
      id: 'basic',
      name: '基础木鱼',
      imagePath: 'assets/images/fish_basic.png',
      unlockThreshold: 0,
      rarity: FishRarity.basic,
      isUnlocked: true,
    ),
    WoodenFish(
      id: 'bronze',
      name: '青铜木鱼',
      imagePath: 'assets/images/fish_bronze.png',
      unlockThreshold: 100,
      rarity: FishRarity.bronze,
    ),
    WoodenFish(
      id: 'silver',
      name: '白银木鱼',
      imagePath: 'assets/images/fish_silver.png',
      unlockThreshold: 500,
      rarity: FishRarity.silver,
    ),
    WoodenFish(
      id: 'gold',
      name: '黄金木鱼',
      imagePath: 'assets/images/fish_gold.png',
      unlockThreshold: 1000,
      rarity: FishRarity.gold,
    ),
    WoodenFish(
      id: 'platinum',
      name: '铂金木鱼',
      imagePath: 'assets/images/fish_platinum.png',
      unlockThreshold: 2000,
      rarity: FishRarity.platinum,
    ),
    WoodenFish(
      id: 'diamond',
      name: '钻石木鱼',
      imagePath: 'assets/images/fish_diamond.png',
      unlockThreshold: 5000,
      rarity: FishRarity.diamond,
    ),
    WoodenFish(
      id: 'master',
      name: '大师木鱼',
      imagePath: 'assets/images/fish_master.png',
      unlockThreshold: 10000,
      rarity: FishRarity.master,
    ),
    WoodenFish(
      id: 'king',
      name: '王者木鱼',
      imagePath: 'assets/images/fish_king.png',
      unlockThreshold: 20000,
      rarity: FishRarity.king,
    ),
  ];

  /// 根据ID获取木鱼
  static WoodenFish? getFishById(String id) {
    try {
      return defaultFishList.firstWhere((fish) => fish.id == id);
    } catch (e) {
      return null;
    }
  }

  /// 获取已解锁的木鱼列表
  static List<WoodenFish> getUnlockedFish(Map<String, bool> unlockedFish) {
    return defaultFishList.where((fish) {
      return unlockedFish[fish.id] ?? false;
    }).toList();
  }

  /// 获取可解锁的木鱼（基于总点击次数）
  static List<String> getUnlockableFish(
    int totalTaps,
    Map<String, bool> currentUnlocked,
  ) {
    List<String> unlockable = [];

    for (var fish in defaultFishList) {
      // 如果还未解锁且达到解锁条件
      if (!(currentUnlocked[fish.id] ?? false) &&
          totalTaps >= fish.unlockThreshold) {
        unlockable.add(fish.id);
      }
    }

    return unlockable;
  }
}

/// 应用主题配置
class AppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      primarySwatch: MaterialColor(AppColors.primary.value, <int, Color>{
        50: AppColors.primary.withOpacity(0.1),
        100: AppColors.primary.withOpacity(0.2),
        200: AppColors.primary.withOpacity(0.3),
        300: AppColors.primary.withOpacity(0.4),
        400: AppColors.primary.withOpacity(0.5),
        500: AppColors.primary,
        600: AppColors.primary.withOpacity(0.7),
        700: AppColors.primary.withOpacity(0.8),
        800: AppColors.primary.withOpacity(0.9),
        900: AppColors.primaryDark,
      }),
      primaryColor: AppColors.primary,
      scaffoldBackgroundColor: AppColors.backgroundStart,
      appBarTheme: const AppBarTheme(
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textLight,
        elevation: 0,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.button,
          foregroundColor: AppColors.textPrimary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(
              AppConstants.defaultBorderRadius,
            ),
          ),
        ),
      ),
      textTheme: const TextTheme(
        headlineLarge: TextStyle(
          color: AppColors.textPrimary,
          fontSize: AppConstants.titleFontSize,
          fontWeight: FontWeight.bold,
        ),
        bodyLarge: TextStyle(
          color: AppColors.textPrimary,
          fontSize: AppConstants.counterFontSize,
        ),
        bodyMedium: TextStyle(
          color: AppColors.textSecondary,
          fontSize: AppConstants.buttonFontSize,
        ),
      ),
      cardTheme: CardThemeData(
        color: AppColors.cardBackground,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        ),
      ),
    );
  }
}

/// 默认浮现文字列表配置
class FloatingTextConfig {
  static final List<FloatingText> defaultTextList = [
    FloatingText(
      id: 'merit_1',
      text: '功德+1',
      color: const Color(0xFF4CAF50),
      unlockThreshold: 0,
      rarity: FloatingTextRarity.basic,
      isUnlocked: true,
    ),
    FloatingText(
      id: 'luck_1',
      text: '好运+1',
      color: const Color(0xFF2196F3),
      unlockThreshold: 0,
      rarity: FloatingTextRarity.basic,
      isUnlocked: true,
    ),
    FloatingText(
      id: 'peace_1',
      text: '平安+1',
      color: const Color(0xFF9C27B0),
      unlockThreshold: 50,
      rarity: FloatingTextRarity.bronze,
    ),
    FloatingText(
      id: 'wisdom_1',
      text: '智慧+1',
      color: const Color(0xFFFF9800),
      unlockThreshold: 100,
      rarity: FloatingTextRarity.bronze,
    ),
    FloatingText(
      id: 'health_1',
      text: '健康+1',
      color: const Color(0xFFE91E63),
      unlockThreshold: 200,
      rarity: FloatingTextRarity.silver,
    ),
    FloatingText(
      id: 'fortune_1',
      text: '财运+1',
      color: const Color(0xFFFFD700),
      unlockThreshold: 500,
      rarity: FloatingTextRarity.silver,
    ),
    FloatingText(
      id: 'love_1',
      text: '桃花+1',
      color: const Color(0xFFFF69B4),
      unlockThreshold: 1000,
      rarity: FloatingTextRarity.gold,
    ),
    FloatingText(
      id: 'career_1',
      text: '事业+1',
      color: const Color(0xFF795548),
      unlockThreshold: 1500,
      rarity: FloatingTextRarity.gold,
    ),
    FloatingText(
      id: 'enlightenment_1',
      text: '开悟+1',
      color: const Color(0xFF607D8B),
      unlockThreshold: 3000,
      rarity: FloatingTextRarity.platinum,
    ),
    FloatingText(
      id: 'transcendence_1',
      text: '超脱+1',
      color: const Color(0xFFB9F2FF),
      unlockThreshold: 5000,
      rarity: FloatingTextRarity.diamond,
    ),
    FloatingText(
      id: 'nirvana_1',
      text: '涅槃+1',
      color: const Color(0xFF654321),
      unlockThreshold: 10000,
      rarity: FloatingTextRarity.master,
    ),
    FloatingText(
      id: 'buddha_1',
      text: '成佛+1',
      color: const Color(0xFF800080),
      unlockThreshold: 20000,
      rarity: FloatingTextRarity.king,
    ),
  ];

  /// 根据ID获取浮现文字
  static FloatingText? getTextById(String id) {
    try {
      return defaultTextList.firstWhere((text) => text.id == id);
    } catch (e) {
      return null;
    }
  }

  /// 获取已解锁的浮现文字列表
  static List<FloatingText> getUnlockedTexts(Map<String, bool> unlockedTexts) {
    return defaultTextList.where((text) {
      return unlockedTexts[text.id] ?? false;
    }).toList();
  }

  /// 获取可解锁的浮现文字（基于总点击次数）
  static List<String> getUnlockableTexts(
    int totalTaps,
    Map<String, bool> currentUnlocked,
  ) {
    List<String> unlockable = [];

    for (var text in defaultTextList) {
      // 如果还未解锁且达到解锁条件
      if (!(currentUnlocked[text.id] ?? false) &&
          totalTaps >= text.unlockThreshold) {
        unlockable.add(text.id);
      }
    }

    return unlockable;
  }
}
