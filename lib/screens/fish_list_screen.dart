import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/game_provider.dart';
import '../models/wooden_fish.dart';
import '../utils/constants.dart';
import '../utils/fish_image.dart';

/// 木鱼列表界面
class FishListScreen extends StatelessWidget {
  const FishListScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '木鱼收藏',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textLight,
        elevation: 0,
        centerTitle: true,
      ),
      body: Container(
        decoration: const BoxDecoration(gradient: AppColors.backgroundGradient),
        child: Consumer<GameProvider>(
          builder: (context, gameProvider, child) {
            if (gameProvider.isLoading) {
              return const Center(
                child: CircularProgressIndicator(color: AppColors.primary),
              );
            }

            return Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: GridView.builder(
                physics: const BouncingScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 20,
                  childAspectRatio: 0.85, // 调整宽高比，让卡片更协调
                ),
                itemCount: gameProvider.fishList.length,
                itemBuilder: (context, index) {
                  final fish = gameProvider.fishList[index];
                  return _buildFishCard(context, gameProvider, fish);
                },
              ),
            );
          },
        ),
      ),
    );
  }

  /// 构建木鱼卡片
  Widget _buildFishCard(
    BuildContext context,
    GameProvider gameProvider,
    WoodenFish fish,
  ) {
    final isUnlocked = fish.isUnlocked;
    final isCurrent = gameProvider.userData.currentFishId == fish.id;
    final progress = gameProvider.getFishUnlockProgress(fish.id);

    return GestureDetector(
      onTap: () => _onFishTap(context, gameProvider, fish),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        child: Card(
          elevation: isCurrent ? 8 : 4,
          shadowColor: AppColors.cardShadow.withValues(alpha: 0.3),
          color: AppColors.cardBackground,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
            side: isCurrent
                ? const BorderSide(color: AppColors.primary, width: 2.5)
                : BorderSide.none,
          ),
          child: Container(
            padding: const EdgeInsets.all(12),
            child: Column(
              children: [
                // 木鱼图片容器
                Expanded(
                  flex: 6,
                  child: Container(
                    width: double.infinity,
                    margin: const EdgeInsets.only(bottom: 8),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      color: isUnlocked
                          ? AppColors.backgroundStart.withValues(alpha: 0.3)
                          : Colors.black.withValues(alpha: 0.05),
                      boxShadow: isUnlocked
                          ? [
                              BoxShadow(
                                color: AppColors.primary.withValues(alpha: 0.1),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ]
                          : null,
                    ),
                    child: Center(
                      child: FishImage(
                        fishId: fish.id,
                        size: 85.0, // 进一步增大图片尺寸
                        isUnlocked: isUnlocked,
                      ),
                    ),
                  ),
                ),

                // 木鱼名称
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: Text(
                    fish.name,
                    style: TextStyle(
                      fontSize: 14.0, // 增大字体
                      fontWeight: FontWeight.bold,
                      color: isUnlocked
                          ? AppColors.textPrimary
                          : AppColors.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),

                // 稀有度标签
                Container(
                  margin: const EdgeInsets.only(bottom: 6),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: Color(fish.rarity.color).withValues(alpha: 0.15),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Color(fish.rarity.color),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    fish.rarity.displayName,
                    style: TextStyle(
                      fontSize: 10.0, // 稍微增大字体
                      color: Color(fish.rarity.color),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),

                // 解锁状态或进度
                SizedBox(
                  height: 20, // 增加高度
                  child: isUnlocked
                      ? _buildUnlockedStatus(isCurrent)
                      : _buildUnlockProgress(
                          fish,
                          progress,
                          gameProvider.totalTaps,
                        ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建已解锁状态
  Widget _buildUnlockedStatus(bool isCurrent) {
    return Container(
      width: double.infinity,
      height: 20,
      decoration: BoxDecoration(
        color: isCurrent ? AppColors.primary : AppColors.success,
        borderRadius: BorderRadius.circular(6),
        boxShadow: [
          BoxShadow(
            color: (isCurrent ? AppColors.primary : AppColors.success)
                .withValues(alpha: 0.3),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Center(
        child: Text(
          isCurrent ? '使用中' : '已解锁',
          style: const TextStyle(
            fontSize: 10.0, // 增大字体
            color: AppColors.textLight,
            fontWeight: FontWeight.w600,
          ),
          textAlign: TextAlign.center,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }

  /// 构建解锁进度
  Widget _buildUnlockProgress(WoodenFish fish, double progress, int totalTaps) {
    final remaining = fish.unlockThreshold - totalTaps;

    return Container(
      width: double.infinity,
      height: 20,
      decoration: BoxDecoration(
        color: AppColors.divider.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: AppColors.divider, width: 1),
      ),
      child: Center(
        child: Text(
          remaining > 0 ? '还需 $remaining 次' : '可解锁！',
          style: TextStyle(
            fontSize: 10.0, // 增大字体
            color: remaining > 0 ? AppColors.textSecondary : AppColors.success,
            fontWeight: remaining > 0 ? FontWeight.w500 : FontWeight.bold,
          ),
          textAlign: TextAlign.center,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }

  /// 处理木鱼点击事件
  void _onFishTap(
    BuildContext context,
    GameProvider gameProvider,
    WoodenFish fish,
  ) {
    if (fish.isUnlocked) {
      // 如果已解锁，切换使用
      gameProvider.switchFish(fish.id);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('已切换到 ${fish.name}'),
          duration: const Duration(seconds: 1),
          backgroundColor: AppColors.success,
        ),
      );
    } else {
      // 如果未解锁，显示解锁条件
      _showUnlockDialog(context, fish, gameProvider.totalTaps);
    }
  }

  /// 显示解锁条件对话框
  void _showUnlockDialog(BuildContext context, WoodenFish fish, int totalTaps) {
    final remaining = fish.unlockThreshold - totalTaps;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(fish.name),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('稀有度: ${fish.rarity.displayName}'),
              const SizedBox(height: 8),
              Text('解锁条件: ${fish.unlockThreshold} 次点击'),
              const SizedBox(height: 8),
              if (remaining > 0)
                Text(
                  '还需要: $remaining 次点击',
                  style: const TextStyle(
                    color: AppColors.warning,
                    fontWeight: FontWeight.bold,
                  ),
                )
              else
                const Text(
                  '已达到解锁条件！',
                  style: TextStyle(
                    color: AppColors.success,
                    fontWeight: FontWeight.bold,
                  ),
                ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }
}
