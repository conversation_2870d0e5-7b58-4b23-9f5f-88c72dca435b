import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/game_provider.dart';
import '../utils/constants.dart';

/// 设置界面
class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('设置'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textLight,
        elevation: 0,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.backgroundGradient,
        ),
        child: ListView(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          children: [
            // 应用信息卡片
            _buildAppInfoCard(),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // 数据管理卡片
            _buildDataManagementCard(context),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // 关于卡片
            _buildAboutCard(context),
          ],
        ),
      ),
    );
  }

  /// 构建应用信息卡片
  Widget _buildAppInfoCard() {
    return Card(
      color: AppColors.cardBackground,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '应用信息',
              style: TextStyle(
                fontSize: AppConstants.titleFontSize,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            _buildInfoRow('应用名称', AppConstants.appName),
            _buildInfoRow('版本号', AppConstants.appVersion),
            _buildInfoRow('应用描述', AppConstants.appDescription),
          ],
        ),
      ),
    );
  }

  /// 构建数据管理卡片
  Widget _buildDataManagementCard(BuildContext context) {
    return Card(
      color: AppColors.cardBackground,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '数据管理',
              style: TextStyle(
                fontSize: AppConstants.titleFontSize,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            
            // 游戏统计
            Consumer<GameProvider>(
              builder: (context, gameProvider, child) {
                return Column(
                  children: [
                    _buildInfoRow('今日点击', '${gameProvider.todayTaps} 次'),
                    _buildInfoRow('总点击次数', '${gameProvider.totalTaps} 次'),
                    _buildInfoRow('已解锁木鱼', '${gameProvider.getUnlockedFishList().length} 个'),
                    _buildInfoRow('当前木鱼', gameProvider.currentFishName),
                  ],
                );
              },
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // 重置数据按钮
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => _showResetDialog(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.warning,
                  foregroundColor: AppColors.textLight,
                ),
                child: const Text('重置游戏数据'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建关于卡片
  Widget _buildAboutCard(BuildContext context) {
    return Card(
      color: AppColors.cardBackground,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '关于',
              style: TextStyle(
                fontSize: AppConstants.titleFontSize,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            
            ListTile(
              leading: const Icon(Icons.info_outline, color: AppColors.primary),
              title: const Text('关于应用'),
              subtitle: const Text('了解更多关于数字木鱼的信息'),
              onTap: () => _showAboutDialog(context),
              contentPadding: EdgeInsets.zero,
            ),
            
            ListTile(
              leading: const Icon(Icons.contact_mail, color: AppColors.primary),
              title: const Text('联系我们'),
              subtitle: const Text('反馈问题或建议'),
              onTap: () => _showContactDialog(context),
              contentPadding: EdgeInsets.zero,
            ),
            
            ListTile(
              leading: const Icon(Icons.favorite, color: AppColors.error),
              title: const Text('支持开发'),
              subtitle: const Text('给我们一个好评'),
              onTap: () => _showSupportDialog(context),
              contentPadding: EdgeInsets.zero,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建信息行
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              color: AppColors.textSecondary,
              fontSize: AppConstants.buttonFontSize,
            ),
          ),
          Flexible(
            child: Text(
              value,
              style: const TextStyle(
                color: AppColors.textPrimary,
                fontSize: AppConstants.buttonFontSize,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }

  /// 显示重置数据对话框
  void _showResetDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('重置游戏数据'),
          content: const Text(
            '确定要重置所有游戏数据吗？\n\n这将清除：\n• 所有点击记录\n• 木鱼解锁状态\n• 游戏进度\n\n此操作无法撤销！',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _resetGameData(context);
              },
              style: TextButton.styleFrom(
                foregroundColor: AppColors.error,
              ),
              child: const Text('确定重置'),
            ),
          ],
        );
      },
    );
  }

  /// 重置游戏数据
  void _resetGameData(BuildContext context) {
    final gameProvider = Provider.of<GameProvider>(context, listen: false);
    gameProvider.resetGameData();
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('游戏数据已重置'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  /// 显示关于对话框
  void _showAboutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('关于数字木鱼'),
          content: const Text(
            '数字木鱼是一个治愈系的点击应用。\n\n'
            '通过点击屏幕来模拟敲木鱼的体验，'
            '在忙碌的生活中找到片刻的宁静。\n\n'
            '随着点击次数的增加，你可以解锁更多'
            '精美的木鱼收藏品。\n\n'
            '愿你在这里找到内心的平静。',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }

  /// 显示联系对话框
  void _showContactDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('联系我们'),
          content: const Text(
            '如果您有任何问题、建议或反馈，'
            '欢迎通过以下方式联系我们：\n\n'
            '邮箱：<EMAIL>\n'
            '微信：DigitalWoodenFish\n\n'
            '我们会尽快回复您的消息。',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }

  /// 显示支持对话框
  void _showSupportDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('支持开发'),
          content: const Text(
            '如果您喜欢数字木鱼，请考虑：\n\n'
            '• 在应用商店给我们五星好评\n'
            '• 推荐给您的朋友\n'
            '• 分享到社交媒体\n\n'
            '您的支持是我们持续改进的动力！',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }
}
