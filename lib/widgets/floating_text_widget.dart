import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../models/floating_text.dart';

/// 浮现文字动画组件
class FloatingTextWidget extends StatefulWidget {
  final FloatingText floatingText;
  final Offset startPosition;
  final VoidCallback? onAnimationComplete;

  const FloatingTextWidget({
    super.key,
    required this.floatingText,
    required this.startPosition,
    this.onAnimationComplete,
  });

  @override
  State<FloatingTextWidget> createState() => _FloatingTextWidgetState();
}

class _FloatingTextWidgetState extends State<FloatingTextWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();

    // 创建动画控制器，缩短动画时间提高性能
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // 向上滑动动画，使用更平滑的曲线
    _slideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(0, -80),
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeOutCubic));

    // 缩放动画，减少弹性效果提高性能
    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.2, curve: Curves.easeOut),
      ),
    );

    // 监听动画完成
    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        widget.onAnimationComplete?.call();
      }
    });

    // 开始动画
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: widget.startPosition.dx - 50, // 居中显示
      top: widget.startPosition.dy - 20,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          // 优化透明度计算，使用更简单的淡入淡出
          double opacity;
          if (_controller.value <= 0.2) {
            // 淡入阶段
            opacity = _controller.value / 0.2;
          } else if (_controller.value >= 0.8) {
            // 淡出阶段
            opacity = 1.0 - ((_controller.value - 0.8) / 0.2);
          } else {
            // 完全显示阶段
            opacity = 1.0;
          }

          return Transform.translate(
            offset: _slideAnimation.value,
            child: Transform.scale(
              scale: _scaleAnimation.value,
              child: Opacity(
                opacity: opacity.clamp(0.0, 1.0),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: widget.floatingText.color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: widget.floatingText.color.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    widget.floatingText.text,
                    style: TextStyle(
                      color: widget.floatingText.color,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      shadows: [
                        Shadow(
                          color: Colors.black.withValues(alpha: 0.3),
                          offset: const Offset(1, 1),
                          blurRadius: 2,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

/// 浮现文字管理器
class FloatingTextManager extends StatefulWidget {
  final Widget child;

  const FloatingTextManager({super.key, required this.child});

  @override
  State<FloatingTextManager> createState() => _FloatingTextManagerState();
}

class _FloatingTextManagerState extends State<FloatingTextManager> {
  final List<FloatingTextWidget> _floatingTexts = [];
  final math.Random _random = math.Random();

  // 配置常量
  static const int maxFloatingTexts = 8; // 最大同时显示的浮动文字数量
  static const double positionOffsetRange = 60.0; // 位置偏移范围

  /// 显示浮现文字
  void showFloatingText(FloatingText textTemplate, Offset position) {
    // 创建新的文字实例
    final textInstance = textTemplate.createInstance();

    // 生成随机位置偏移
    final randomOffset = _generateRandomOffset();
    final adjustedPosition = Offset(
      position.dx + randomOffset.dx,
      position.dy + randomOffset.dy,
    );

    // 如果超过最大数量，移除最早的文字
    if (_floatingTexts.length >= maxFloatingTexts) {
      _removeOldestText();
    }

    final textWidget = FloatingTextWidget(
      floatingText: textInstance,
      startPosition: adjustedPosition,
      onAnimationComplete: () {
        // 动画完成后移除文字（使用instanceId确保精确移除）
        _removeTextByInstanceId(textInstance.instanceId);
      },
    );

    setState(() {
      _floatingTexts.add(textWidget);
    });
  }

  /// 生成随机位置偏移
  Offset _generateRandomOffset() {
    final dx = (_random.nextDouble() - 0.5) * 2 * positionOffsetRange;
    final dy = (_random.nextDouble() - 0.5) * 2 * positionOffsetRange;
    return Offset(dx, dy);
  }

  /// 移除最早的文字
  void _removeOldestText() {
    if (_floatingTexts.isNotEmpty) {
      setState(() {
        _floatingTexts.removeAt(0);
      });
    }
  }

  /// 根据instanceId移除文字
  void _removeTextByInstanceId(String instanceId) {
    setState(() {
      _floatingTexts.removeWhere((widget) {
        return widget.floatingText.instanceId == instanceId;
      });
    });
  }

  /// 清除所有浮动文字
  void clearAllTexts() {
    setState(() {
      _floatingTexts.clear();
    });
  }

  /// 获取当前浮动文字数量
  int get currentTextCount => _floatingTexts.length;

  @override
  Widget build(BuildContext context) {
    return Stack(children: [widget.child, ..._floatingTexts]);
  }
}

/// 全局浮现文字管理器实例
final GlobalKey<_FloatingTextManagerState> _floatingTextManagerKey =
    GlobalKey<_FloatingTextManagerState>();

/// 获取浮现文字管理器的全局Key（用于外部访问）
GlobalKey get floatingTextManagerKey => _floatingTextManagerKey;

/// 显示浮现文字的全局方法
void showFloatingText(FloatingText text, Offset position) {
  _floatingTextManagerKey.currentState?.showFloatingText(text, position);
}

/// 清除所有浮动文字的全局方法
void clearAllFloatingTexts() {
  _floatingTextManagerKey.currentState?.clearAllTexts();
}

/// 获取当前浮动文字数量的全局方法
int getCurrentFloatingTextCount() {
  return _floatingTextManagerKey.currentState?.currentTextCount ?? 0;
}
